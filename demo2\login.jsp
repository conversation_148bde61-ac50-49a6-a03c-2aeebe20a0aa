<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 处理登录逻辑
    String username = request.getParameter("username");
    String password = request.getParameter("pwd");
    String remember = request.getParameter("remember");
    String errorMsg = "";

    // 如果是POST请求，进行登录验证
    if ("POST".equals(request.getMethod()) && username != null && password != null) {
        if ("admin".equals(username) && "123456".equals(password)) {
            // 登录成功，设置session
            session.setAttribute("username", username);

            // 如果选择了记住密码，设置cookie
            if ("on".equals(remember)) {
                Cookie usernameCookie = new Cookie("username", username);
                Cookie passwordCookie = new Cookie("password", password);
                usernameCookie.setMaxAge(7 * 24 * 60 * 60); // 保存7天
                passwordCookie.setMaxAge(7 * 24 * 60 * 60); // 保存7天
                response.addCookie(usernameCookie);
                response.addCookie(passwordCookie);
            }

            // 重定向到index.jsp
            response.sendRedirect("index.jsp");
            return;
        } else {
            errorMsg = "用户名或密码错误，请重新输入！";
        }
    }

    // 从cookie中获取保存的用户名和密码
    String savedUsername = "";
    String savedPassword = "";
    Cookie[] cookies = request.getCookies();
    if (cookies != null) {
        for (Cookie cookie : cookies) {
            if ("username".equals(cookie.getName())) {
                savedUsername = cookie.getValue();
            } else if ("password".equals(cookie.getName())) {
                savedPassword = cookie.getValue();
            }
        }
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>用户登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 50px;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .login-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .checkbox-group {
            margin: 15px 0;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
        .button-group {
            text-align: center;
            margin-top: 25px;
        }
        input[type="submit"], input[type="reset"] {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        input[type="submit"] {
            background-color: #007bff;
            color: white;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
        input[type="reset"] {
            background-color: #6c757d;
            color: white;
        }
        input[type="reset"]:hover {
            background-color: #545b62;
        }
        .error-msg {
            color: #dc3545;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
        .register-link a {
            color: #007bff;
            text-decoration: none;
        }
        .register-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="login-title">用户登录</h2>

        <% if (!errorMsg.isEmpty()) { %>
            <div class="error-msg"><%= errorMsg %></div>
        <% } %>

        <form method="post" action="login.jsp">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="<%= savedUsername %>" required />
            </div>

            <div class="form-group">
                <label for="pwd">密码:</label>
                <input type="password" id="pwd" name="pwd" value="<%= savedPassword %>" required />
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="remember" name="remember" <%= !savedUsername.isEmpty() ? "checked" : "" %> />
                <label for="remember" style="display: inline; font-weight: normal;">记住用户名和密码</label>
            </div>

            <div class="button-group">
                <input type="submit" value="登录" />
                <input type="reset" value="重置" />
            </div>
        </form>

        <div class="register-link">
            <p>还没有账号？<a href="register.html">立即注册</a></p>
        </div>
    </div>
</body>
</html>