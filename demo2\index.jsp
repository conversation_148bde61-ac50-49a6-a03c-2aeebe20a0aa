<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 检查用户是否已登录
    String username = (String) session.getAttribute("username");
    if (username == null) {
        // 如果没有登录，重定向到登录页面
        response.sendRedirect("login.jsp");
        return;
    }

    // 处理注销请求
    String action = request.getParameter("action");
    if ("logout".equals(action)) {
        // 清除session
        session.invalidate();

        // 清除cookie
        Cookie usernameCookie = new Cookie("username", "");
        Cookie passwordCookie = new Cookie("password", "");
        usernameCookie.setMaxAge(0);
        passwordCookie.setMaxAge(0);
        response.addCookie(usernameCookie);
        response.addCookie(passwordCookie);

        // 重定向到登录页面
        response.sendRedirect("login.jsp");
        return;
    }

    // 获取所有cookie信息用于显示
    StringBuilder cookieInfo = new StringBuilder();
    Cookie[] cookies = request.getCookies();
    if (cookies != null && cookies.length > 0) {
        cookieInfo.append("当前Cookie信息：<br/>");
        for (Cookie cookie : cookies) {
            cookieInfo.append("名称: ").append(cookie.getName())
                     .append(", 值: ").append(cookie.getValue())
                     .append(", 最大存活时间: ").append(cookie.getMaxAge()).append("秒<br/>");
        }
    } else {
        cookieInfo.append("当前没有Cookie信息");
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>用户主页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 30px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .welcome-msg {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 10px;
        }
        .info-section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .info-content {
            line-height: 1.6;
            color: #555;
        }
        .user-info {
            background-color: #e8f5e8;
            border-left-color: #28a745;
        }
        .cookie-info {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .button-group {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 25px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .btn-logout {
            background-color: #dc3545;
            color: white;
        }
        .btn-logout:hover {
            background-color: #c82333;
        }
        .btn-register {
            background-color: #28a745;
            color: white;
        }
        .btn-register:hover {
            background-color: #218838;
        }
        .current-time {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="welcome-msg">欢迎您，<%= username %>！</h1>
            <p>您已成功登录系统</p>
        </div>

        <div class="info-section user-info">
            <div class="info-title">用户信息</div>
            <div class="info-content">
                <strong>当前登录用户：</strong><%= username %><br/>
                <strong>登录时间：</strong><%= new java.util.Date() %><br/>
                <strong>Session ID：</strong><%= session.getId() %><br/>
                <strong>Session 创建时间：</strong><%= new java.util.Date(session.getCreationTime()) %><br/>
                <strong>Session 最后访问时间：</strong><%= new java.util.Date(session.getLastAccessedTime()) %>
            </div>
        </div>

        <div class="info-section cookie-info">
            <div class="info-title">Cookie 信息</div>
            <div class="info-content">
                <%= cookieInfo.toString() %>
            </div>
        </div>

        <div class="button-group">
            <a href="index.jsp?action=logout" class="btn btn-logout"
               onclick="return confirm('确定要注销登录吗？')">注销登录</a>
            <a href="register.html" class="btn btn-register">用户注册</a>
        </div>

        <div class="current-time">
            <p>当前服务器时间：<%= new java.util.Date() %></p>
        </div>
    </div>
</body>
</html>